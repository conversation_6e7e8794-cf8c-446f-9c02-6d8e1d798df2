// 主题色彩配置
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;
@info-color: #1890ff;

// 中性色
@text-color: rgba(0, 0, 0, 0.85);
@text-color-secondary: rgba(0, 0, 0, 0.45);
@text-color-inverse: #fff;
@border-color-base: #d9d9d9;
@border-color-split: #f0f0f0;
@background-color-light: #fafafa;
@background-color-base: #f5f5f5;

// 链接色
@link-color: @primary-color;
@link-hover-color: color(~`colorPalette('@{link-color}', 5) `);
@link-active-color: color(~`colorPalette('@{link-color}', 7) `);

// 阴影
@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);
@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);
@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);
@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);
@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

// 自定义颜色
.text-primary {
  color: @primary-color !important;
}

.text-success {
  color: @success-color !important;
}

.text-warning {
  color: @warning-color !important;
}

.text-error {
  color: @error-color !important;
}

.text-info {
  color: @info-color !important;
}

.bg-primary {
  background-color: @primary-color !important;
}

.bg-success {
  background-color: @success-color !important;
}

.bg-warning {
  background-color: @warning-color !important;
}

.bg-error {
  background-color: @error-color !important;
}

.bg-info {
  background-color: @info-color !important;
}
