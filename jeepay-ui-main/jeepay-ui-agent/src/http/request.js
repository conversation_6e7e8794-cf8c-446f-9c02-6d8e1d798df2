import axios from 'axios'
import { message, notification } from 'ant-design-vue'
import { useUserStore } from '@/store'
import router from '@/router'

// 创建axios实例
const request = axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:9219/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    const userStore = useUserStore()
    
    // 添加token到请求头
    if (userStore.token) {
      config.headers['iToken'] = userStore.token
    }
    
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data
    
    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return response
    }
    
    // 统一处理响应
    if (res.code === 0) {
      return res.data
    } else {
      // 处理业务错误
      message.error(res.msg || '请求失败')
      return Promise.reject(new Error(res.msg || '请求失败'))
    }
  },
  error => {
    console.error('请求错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          const userStore = useUserStore()
          userStore.logout()
          router.push('/user/login')
          message.error('登录已过期，请重新登录')
          break
        case 403:
          message.error('没有权限访问该资源')
          break
        case 404:
          message.error('请求的资源不存在')
          break
        case 500:
          message.error('服务器内部错误')
          break
        default:
          message.error(data?.msg || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      message.error('请求超时，请稍后重试')
    } else {
      message.error('网络错误，请检查网络连接')
    }
    
    return Promise.reject(error)
  }
)

// 封装常用请求方法
export const http = {
  get(url, params = {}) {
    return request({
      method: 'GET',
      url,
      params
    })
  },
  
  post(url, data = {}) {
    return request({
      method: 'POST',
      url,
      data
    })
  },
  
  put(url, data = {}) {
    return request({
      method: 'PUT',
      url,
      data
    })
  },
  
  delete(url, params = {}) {
    return request({
      method: 'DELETE',
      url,
      params
    })
  },
  
  upload(url, formData) {
    return request({
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  download(url, params = {}) {
    return request({
      method: 'GET',
      url,
      params,
      responseType: 'blob'
    })
  }
}

export default request
