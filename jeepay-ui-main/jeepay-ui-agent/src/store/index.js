import { defineStore } from 'pinia'

// 用户状态管理
export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('iToken') || '',
    userInfo: {},
    permissions: [],
    roles: []
  }),
  
  getters: {
    isLogin: (state) => !!state.token,
    hasPermission: (state) => (permission) => {
      return state.permissions.includes(permission)
    }
  },
  
  actions: {
    // 设置token
    setToken(token) {
      this.token = token
      localStorage.setItem('iToken', token)
    },
    
    // 清除token
    clearToken() {
      this.token = ''
      localStorage.removeItem('iToken')
    },
    
    // 设置用户信息
    setUserInfo(userInfo) {
      this.userInfo = userInfo
    },
    
    // 设置权限
    setPermissions(permissions) {
      this.permissions = permissions
    },
    
    // 登出
    logout() {
      this.clearToken()
      this.userInfo = {}
      this.permissions = []
      this.roles = []
    }
  }
})

// 应用状态管理
export const useAppStore = defineStore('app', {
  state: () => ({
    collapsed: false,
    device: 'desktop',
    theme: 'light',
    primaryColor: '#1890ff'
  }),
  
  actions: {
    toggleCollapsed() {
      this.collapsed = !this.collapsed
    },
    
    setDevice(device) {
      this.device = device
    },
    
    setTheme(theme) {
      this.theme = theme
    },
    
    setPrimaryColor(color) {
      this.primaryColor = color
    }
  }
})

export function initStore(app) {
  console.log('Store initialized')
}
