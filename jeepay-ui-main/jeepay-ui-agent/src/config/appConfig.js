/**
 * 全局配置信息， 包含网站标题，  动态组件定义
 *
 * <AUTHOR>
 * @site https://www.jeepay.vip
 * @date 2021/5/8 07:18
 */

/** 应用配置项 **/
export default {
  APP_TITLE: 'Jeepay代理商系统', // 设置浏览器title
  ACCESS_TOKEN_NAME: 'iToken', // 设置请求token的名字， 用于请求header 和 localstorage中存在名称
}

/**
 * 与后端开发人员的路由名称及配置项
 * 组件名称 ：{ 默认跳转路径（如果后端配置则已动态配置为准）， 组件渲染 }
 * */
export const asyncRouteDefine = {
  CurrentUserInfo: {
    defaultPath: '/current/userinfo',
    component: () => import('@/views/current/UserinfoPage.vue'),
  }, // 用户设置

  MainPage: { defaultPath: '/main', component: () => import('@/views/dashboard/Analysis.vue') },
  SysUserPage: {
    defaultPath: '/users',
    component: () => import('@/views/sysuser/SysUserPage.vue'),
  },
  RolePage: { defaultPath: '/roles', component: () => import('@/views/role/RolePage.vue') },

  // 代理商管理
  AgentInfoPage: { 
    defaultPath: '/agent/info', 
    component: () => import('@/views/agent/AgentInfoPage.vue') 
  }, // 代理商信息管理
  SubAgentPage: { 
    defaultPath: '/agent/sub', 
    component: () => import('@/views/agent/SubAgentPage.vue') 
  }, // 下级代理商管理

  // 商户管理
  MchListPage: { 
    defaultPath: '/mch/list', 
    component: () => import('@/views/merchant/MchListPage.vue') 
  }, // 关联商户列表
  MchAppPage: { 
    defaultPath: '/mch/apps', 
    component: () => import('@/views/merchant/MchAppPage.vue') 
  }, // 商户应用列表

  // 订单管理
  PayOrderListPage: {
    defaultPath: '/payOrder',
    component: () => import('@/views/order/pay/PayOrderList.vue'),
  }, // 支付订单列表
  RefundOrderListPage: {
    defaultPath: '/refundOrder',
    component: () => import('@/views/order/refund/RefundOrderList.vue'),
  }, // 退款订单列表
  TransferOrderListPage: {
    defaultPath: '/transferOrder',
    component: () => import('@/views/order/transfer/TransferOrderList.vue'),
  }, // 转账订单

  // 分润管理
  ProfitRecordPage: {
    defaultPath: '/profit/record',
    component: () => import('@/views/profit/ProfitRecordPage.vue'),
  }, // 分润记录

  // 分账管理
  DivisionReceiverGroupPage: {
    defaultPath: '/divisionReceiverGroup',
    component: () => import('@/views/division/group/DivisionReceiverGroupPage.vue'),
  }, // 分账账号组管理
  DivisionReceiverPage: {
    defaultPath: '/divisionReceiver',
    component: () => import('@/views/division/receiver/DivisionReceiverPage.vue'),
  }, // 分账账号管理
  DivisionRecordPage: {
    defaultPath: '/divisionRecord',
    component: () => import('@/views/division/record/DivisionRecordPage.vue'),
  }, // 分账记录
}
