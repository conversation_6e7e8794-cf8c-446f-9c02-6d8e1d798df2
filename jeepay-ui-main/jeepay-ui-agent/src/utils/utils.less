// 工具类样式

// 文本对齐
.text-left {
  text-align: left !important;
}

.text-center {
  text-align: center !important;
}

.text-right {
  text-align: right !important;
}

// 浮动
.float-left {
  float: left !important;
}

.float-right {
  float: right !important;
}

.clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 显示/隐藏
.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-flex {
  display: flex !important;
}

// 边距
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mr-0 { margin-right: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }

.m-1 { margin: 8px !important; }
.mt-1 { margin-top: 8px !important; }
.mr-1 { margin-right: 8px !important; }
.mb-1 { margin-bottom: 8px !important; }
.ml-1 { margin-left: 8px !important; }

.m-2 { margin: 16px !important; }
.mt-2 { margin-top: 16px !important; }
.mr-2 { margin-right: 16px !important; }
.mb-2 { margin-bottom: 16px !important; }
.ml-2 { margin-left: 16px !important; }

.m-3 { margin: 24px !important; }
.mt-3 { margin-top: 24px !important; }
.mr-3 { margin-right: 24px !important; }
.mb-3 { margin-bottom: 24px !important; }
.ml-3 { margin-left: 24px !important; }

// 内边距
.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pr-0 { padding-right: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }

.p-1 { padding: 8px !important; }
.pt-1 { padding-top: 8px !important; }
.pr-1 { padding-right: 8px !important; }
.pb-1 { padding-bottom: 8px !important; }
.pl-1 { padding-left: 8px !important; }

.p-2 { padding: 16px !important; }
.pt-2 { padding-top: 16px !important; }
.pr-2 { padding-right: 16px !important; }
.pb-2 { padding-bottom: 16px !important; }
.pl-2 { padding-left: 16px !important; }

.p-3 { padding: 24px !important; }
.pt-3 { padding-top: 24px !important; }
.pr-3 { padding-right: 24px !important; }
.pb-3 { padding-bottom: 24px !important; }
.pl-3 { padding-left: 24px !important; }

// 宽度
.w-100 {
  width: 100% !important;
}

.w-75 {
  width: 75% !important;
}

.w-50 {
  width: 50% !important;
}

.w-25 {
  width: 25% !important;
}

// 高度
.h-100 {
  height: 100% !important;
}

// 边框
.border {
  border: 1px solid #d9d9d9 !important;
}

.border-top {
  border-top: 1px solid #d9d9d9 !important;
}

.border-right {
  border-right: 1px solid #d9d9d9 !important;
}

.border-bottom {
  border-bottom: 1px solid #d9d9d9 !important;
}

.border-left {
  border-left: 1px solid #d9d9d9 !important;
}

.border-0 {
  border: 0 !important;
}

// 圆角
.rounded {
  border-radius: 6px !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

// 阴影
.shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.shadow-sm {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24) !important;
}

.shadow-lg {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

// 光标
.cursor-pointer {
  cursor: pointer !important;
}

.cursor-default {
  cursor: default !important;
}

// 溢出
.overflow-hidden {
  overflow: hidden !important;
}

.overflow-auto {
  overflow: auto !important;
}

// 文本省略
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
