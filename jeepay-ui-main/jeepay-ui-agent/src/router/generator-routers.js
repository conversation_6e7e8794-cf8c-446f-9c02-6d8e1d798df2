import { asyncRouteDefine } from '@/config/appConfig'
import { BasicLayout, BlankLayout, PageView, RouteView } from '@/layouts'

// 前端路由表
const constantRouterComponents = {
  // 基础页面 layout 必须引入
  BasicLayout: BasicLayout,
  BlankLayout: BlankLayout,
  RouteView: RouteView,
  PageView: PageView,
  '403': () => import('@/views/exception/403.vue'),
  '404': () => import('@/views/exception/404.vue'),
  '500': () => import('@/views/exception/500.vue'),
}

// 根级菜单
const rootRouter = {
  key: '',
  name: 'index',
  path: '',
  component: 'BasicLayout',
  redirect: '/main',
  meta: {
    title: '首页'
  },
  children: []
}

/**
 * 动态生成菜单
 * @param token
 * @returns {Promise<Router>}
 */
export const generatorDynamicRouter = (token) => {
  return new Promise((resolve, reject) => {
    // 这里应该调用后端API获取用户权限菜单
    // 暂时使用模拟数据
    const mockMenuData = [
      {
        entId: 'MainPage',
        entName: '主页',
        entType: 'ML',
        icon: 'dashboard',
        entSort: 10,
        children: []
      },
      {
        entId: 'AgentMgr',
        entName: '代理商管理',
        entType: 'MO',
        icon: 'team',
        entSort: 20,
        children: [
          {
            entId: 'AgentInfoPage',
            entName: '代理商信息',
            entType: 'ML',
            icon: 'user',
            entSort: 10
          },
          {
            entId: 'SubAgentPage',
            entName: '下级代理商',
            entType: 'ML',
            icon: 'usergroup-add',
            entSort: 20
          }
        ]
      },
      {
        entId: 'MchMgr',
        entName: '商户管理',
        entType: 'MO',
        icon: 'shop',
        entSort: 30,
        children: [
          {
            entId: 'MchListPage',
            entName: '关联商户',
            entType: 'ML',
            icon: 'shop',
            entSort: 10
          },
          {
            entId: 'MchAppPage',
            entName: '商户应用',
            entType: 'ML',
            icon: 'appstore',
            entSort: 20
          }
        ]
      },
      {
        entId: 'OrderMgr',
        entName: '订单管理',
        entType: 'MO',
        icon: 'transaction',
        entSort: 40,
        children: [
          {
            entId: 'PayOrderListPage',
            entName: '支付订单',
            entType: 'ML',
            icon: 'pay-circle',
            entSort: 10
          },
          {
            entId: 'RefundOrderListPage',
            entName: '退款订单',
            entType: 'ML',
            icon: 'rollback',
            entSort: 20
          },
          {
            entId: 'TransferOrderListPage',
            entName: '转账订单',
            entType: 'ML',
            icon: 'swap',
            entSort: 30
          }
        ]
      },
      {
        entId: 'ProfitMgr',
        entName: '分润管理',
        entType: 'MO',
        icon: 'dollar',
        entSort: 50,
        children: [
          {
            entId: 'ProfitRecordPage',
            entName: '分润记录',
            entType: 'ML',
            icon: 'money-collect',
            entSort: 10
          }
        ]
      },
      {
        entId: 'SysMgr',
        entName: '系统管理',
        entType: 'MO',
        icon: 'setting',
        entSort: 90,
        children: [
          {
            entId: 'SysUserPage',
            entName: '用户管理',
            entType: 'ML',
            icon: 'user',
            entSort: 10
          },
          {
            entId: 'RolePage',
            entName: '角色管理',
            entType: 'ML',
            icon: 'team',
            entSort: 20
          }
        ]
      }
    ]

    const menuNav = []
    const childrenNav = []

    // 后端数据, 根级树数组,  根级 PID
    listToTree(mockMenuData, childrenNav, 'ROOT')
    rootRouter.children = childrenNav
    menuNav.push(rootRouter)
    
    const routers = generator(menuNav)
    routers.push({
      path: '/:pathMatch(.*)*',
      redirect: '/404',
      hidden: true
    })
    
    resolve(routers)
  })
}

/**
 * 格式化树形结构数据 生成 vue-router 层级路由表
 *
 * @param routerMap
 * @param parent
 * @returns {*}
 */
export const generator = (routerMap, parent) => {
  return routerMap.map(item => {
    const { title, show, hideChildren, hiddenHeaderContent, target, icon } = item.meta || {}
    const currentRouter = {
      // 如果路由设置了 path，则作为默认 path，否则 路由地址 动态拼接生成如 /dashboard/workplace
      path: item.path || `${parent && parent.path || ''}/${item.key}`,
      // 路由名称，建议唯一
      name: item.name || item.key || '',
      // 该路由对应页面的 组件 :方案1
      // component: constantRouterComponents[item.component || item.key],
      // 该路由对应页面的 组件 :方案2 (动态加载)
      component: (constantRouterComponents[item.component || item.key]) || (() => import(`@/views/${item.component}`)),

      // meta: 页面标题, 菜单图标, 页面权限(供指令权限用，可去掉)
      meta: {
        title: title || item.name,
        icon: icon || item.icon || undefined,
        hiddenHeaderContent: hiddenHeaderContent,
        target: target,
        permission: item.name
      }
    }
    // 是否设置了隐藏菜单
    if (show === false) {
      currentRouter.hidden = true
    }
    // 是否设置了隐藏子菜单
    if (hideChildren) {
      currentRouter.hideChildrenInMenu = true
    }
    // 为了防止出现后端返回结果不规范，处理有可能出现拼接出两个 反斜杠
    if (!currentRouter.path.startsWith('http')) {
      currentRouter.path = currentRouter.path.replace('//', '/')
    }
    // 重定向
    item.redirect && (currentRouter.redirect = item.redirect)
    // 是否有子菜单，并递归处理
    if (item.children && item.children.length > 0) {
      // Recursion
      currentRouter.children = generator(item.children, currentRouter)
    }
    return currentRouter
  })
}

/**
 * 数组转树形结构
 * @param list 源数组
 * @param tree 树
 * @param parentId 父ID
 */
const listToTree = (list, tree, parentId) => {
  list.forEach(item => {
    // 判断是否为父级菜单
    if (item.parentId === parentId) {
      const child = {
        ...item,
        key: item.entId,
        name: item.entId,
        component: item.entId,
        children: []
      }
      // 迭代 list， 找到当前菜单相符合的所有子菜单
      listToTree(list, child.children, item.entId)
      // 删掉不存在 children 值的属性
      if (child.children.length <= 0) {
        delete child.children
      }
      // 加入到树中
      tree.push(child)
    }
  })
}
