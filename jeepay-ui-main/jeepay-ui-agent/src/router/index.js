import { createRouter, createWebHistory } from 'vue-router'
import { generatorDynamicRouter } from './generator-routers'

// 基础路由配置
const constantRoutes = [
  {
    path: '/user',
    component: () => import('@/layouts/UserLayout.vue'),
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import('@/views/user/Login.vue')
      }
    ]
  },
  {
    path: '/404',
    component: () => import('@/views/exception/404.vue'),
    hidden: true
  },
  {
    path: '/403',
    component: () => import('@/views/exception/403.vue'),
    hidden: true
  },
  {
    path: '/500',
    component: () => import('@/views/exception/500.vue'),
    hidden: true
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes
})

export default router

export function initRouter(app) {
  // 动态路由生成器会在用户登录后调用
  console.log('Router initialized')
}
