import { createRouter, createWebHistory } from 'vue-router'
import BasicLayout from '@/layouts/BasicLayout.vue'
import UserLayout from '@/layouts/UserLayout.vue'

// 基础路由配置
const routes = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import('@/views/user/Login.vue')
      }
    ]
  },
  {
    path: '/',
    component: BasicLayout,
    redirect: '/main',
    children: [
      {
        path: 'main',
        name: 'main',
        component: () => import('@/views/dashboard/Analysis.vue')
      },
      {
        path: 'current/userinfo',
        name: 'userinfo',
        component: () => import('@/views/current/UserinfoPage.vue')
      },
      {
        path: 'agent/info',
        name: 'agentInfo',
        component: () => import('@/views/agent/AgentInfoPage.vue')
      },
      {
        path: 'agent/sub',
        name: 'subAgent',
        component: () => import('@/views/agent/SubAgentPage.vue')
      },
      {
        path: 'mch/list',
        name: 'mchList',
        component: () => import('@/views/merchant/MchListPage.vue')
      },
      {
        path: 'mch/apps',
        name: 'mchApps',
        component: () => import('@/views/merchant/MchAppPage.vue')
      }
    ]
  },
  {
    path: '/404',
    component: () => import('@/views/exception/404.vue'),
    hidden: true
  },
  {
    path: '/403',
    component: () => import('@/views/exception/403.vue'),
    hidden: true
  },
  {
    path: '/500',
    component: () => import('@/views/exception/500.vue'),
    hidden: true
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
    hidden: true
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('iToken')
  
  if (to.path === '/user/login') {
    // 如果已经登录，跳转到主页
    if (token) {
      next('/')
    } else {
      next()
    }
  } else {
    // 需要登录的页面
    if (token) {
      next()
    } else {
      next('/user/login')
    }
  }
})

export default router

export function initRouter(app) {
  console.log('Router initialized')
}
