<template>
  <div class="dashboard">
    <a-row :gutter="24">
      <!-- 统计卡片 -->
      <a-col :xs="24" :sm="12" :md="6" v-for="item in statisticsData" :key="item.key">
        <a-card :bordered="false" class="statistics-card">
          <a-statistic
            :title="item.title"
            :value="item.value"
            :precision="item.precision || 0"
            :value-style="{ color: item.color }"
            :prefix="item.prefix"
            :suffix="item.suffix"
          />
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="24" style="margin-top: 24px">
      <!-- 图表区域 -->
      <a-col :xs="24" :lg="12">
        <a-card title="最近7天交易趋势" :bordered="false">
          <div ref="chartRef" style="height: 300px"></div>
        </a-card>
      </a-col>
      
      <a-col :xs="24" :lg="12">
        <a-card title="快捷操作" :bordered="false">
          <div class="quick-actions">
            <a-row :gutter="16">
              <a-col :span="12" v-for="action in quickActions" :key="action.key">
                <div class="action-item" @click="handleQuickAction(action.key)">
                  <div class="action-icon">
                    <component :is="action.icon" />
                  </div>
                  <div class="action-text">{{ action.text }}</div>
                </div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="24" style="margin-top: 24px">
      <!-- 最新动态 -->
      <a-col :xs="24" :lg="16">
        <a-card title="最新动态" :bordered="false">
          <a-list
            :data-source="recentActivities"
            :split="false"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: item.color }">
                      <component :is="item.icon" />
                    </a-avatar>
                  </template>
                  <template #title>
                    <span>{{ item.title }}</span>
                  </template>
                  <template #description>
                    <span>{{ item.description }}</span>
                  </template>
                </a-list-item-meta>
                <div>{{ item.time }}</div>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
      
      <a-col :xs="24" :lg="8">
        <a-card title="系统信息" :bordered="false">
          <div class="system-info">
            <div class="info-item">
              <span class="label">系统版本：</span>
              <span class="value">v3.1.0</span>
            </div>
            <div class="info-item">
              <span class="label">运行时间：</span>
              <span class="value">15天3小时</span>
            </div>
            <div class="info-item">
              <span class="label">服务状态：</span>
              <a-tag color="green">正常</a-tag>
            </div>
            <div class="info-item">
              <span class="label">数据库：</span>
              <a-tag color="blue">MySQL 8.0</a-tag>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import {
  TeamOutlined,
  ShopOutlined,
  DollarOutlined,
  TransactionOutlined,
  UserAddOutlined,
  AppstoreOutlined,
  FileTextOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import { mainApi } from '@/api/manage'
import { formatAmount } from '@/utils/util'

const router = useRouter()

// 统计数据
const statisticsData = ref([
  {
    key: 'subAgent',
    title: '下级代理商',
    value: 0,
    color: '#3f8600'
  },
  {
    key: 'merchant',
    title: '关联商户',
    value: 0,
    color: '#1890ff'
  },
  {
    key: 'todayProfit',
    title: '今日分润',
    value: 0,
    precision: 2,
    color: '#cf1322',
    prefix: '¥'
  },
  {
    key: 'monthProfit',
    title: '本月分润',
    value: 0,
    precision: 2,
    color: '#722ed1',
    prefix: '¥'
  }
])

// 加载状态
const loading = ref(false)

// 快捷操作
const quickActions = ref([
  { key: 'addAgent', text: '新增代理商', icon: 'UserAddOutlined' },
  { key: 'addMerchant', text: '添加商户', icon: 'ShopOutlined' },
  { key: 'viewOrders', text: '查看订单', icon: 'FileTextOutlined' },
  { key: 'settings', text: '系统设置', icon: 'SettingOutlined' }
])

// 最新动态
const recentActivities = ref([
  {
    title: '新增代理商',
    description: '代理商"张三"已成功注册',
    time: '2小时前',
    icon: 'TeamOutlined',
    color: '#1890ff'
  },
  {
    title: '商户审核',
    description: '商户"李四便利店"审核通过',
    time: '4小时前',
    icon: 'ShopOutlined',
    color: '#52c41a'
  },
  {
    title: '分润结算',
    description: '本月分润已结算完成',
    time: '1天前',
    icon: 'DollarOutlined',
    color: '#faad14'
  },
  {
    title: '系统更新',
    description: '系统已更新至v3.1.0版本',
    time: '3天前',
    icon: 'SettingOutlined',
    color: '#722ed1'
  }
])

const chartRef = ref()

// 快捷操作处理
const handleQuickAction = (key) => {
  switch (key) {
    case 'addAgent':
      router.push('/agent/sub')
      break
    case 'addMerchant':
      router.push('/mch/list')
      break
    case 'viewOrders':
      router.push('/payOrder')
      break
    case 'settings':
      router.push('/users')
      break
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    loading.value = true

    // 获取主页统计数据
    const countData = await mainApi.getMainCount()
    if (countData) {
      statisticsData.value[0].value = countData.subAgentCount || 0
      statisticsData.value[1].value = countData.mchCount || 0
      statisticsData.value[2].value = (countData.todayProfitAmount || 0) / 100
      statisticsData.value[3].value = (countData.monthProfitAmount || 0) / 100
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initChart = () => {
  // 这里可以使用ECharts等图表库
  // 暂时留空，后续可以添加具体的图表实现
}

onMounted(() => {
  fetchStatistics()
  initChart()
})
</script>

<style lang="less" scoped>
.dashboard {
  .statistics-card {
    margin-bottom: 24px;
    
    :deep(.ant-card-body) {
      padding: 20px 24px 8px;
    }
  }
  
  .quick-actions {
    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s;
      margin-bottom: 16px;
      
      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }
      
      .action-icon {
        font-size: 24px;
        color: #1890ff;
        margin-bottom: 8px;
      }
      
      .action-text {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
  
  .system-info {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .label {
        color: rgba(0, 0, 0, 0.65);
      }
      
      .value {
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
      }
    }
  }
}
</style>
