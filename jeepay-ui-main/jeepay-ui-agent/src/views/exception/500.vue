<template>
  <div class="exception">
    <a-result
      status="500"
      title="500"
      sub-title="抱歉，服务器出现错误。"
    >
      <template #extra>
        <a-button type="primary" @click="goHome">
          返回首页
        </a-button>
      </template>
    </a-result>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style lang="less" scoped>
.exception {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 500px;
}
</style>
