<template>
  <div class="main">
    <a-form
      id="formLogin"
      class="user-layout-login"
      ref="formRef"
      :model="loginForm"
      :rules="loginRules"
      @finish="handleSubmit"
    >
      <a-tabs
        :activeKey="customActiveKey"
        :tabBarStyle="{ textAlign: 'center', borderBottom: 'unset' }"
        @change="handleTabClick"
      >
        <a-tab-pane key="tab1" tab="账号密码登录">
          <a-alert
            v-if="isLoginError"
            type="error"
            showIcon
            style="margin-bottom: 24px"
            :message="loginErrorMsg"
          />
          
          <a-form-item name="username">
            <a-input
              size="large"
              type="text"
              placeholder="用户名"
              v-model:value="loginForm.username"
            >
              <template #prefix>
                <UserOutlined class="prefixIcon" />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item name="password">
            <a-input-password
              size="large"
              placeholder="密码"
              v-model:value="loginForm.password"
            >
              <template #prefix>
                <LockOutlined class="prefixIcon" />
              </template>
            </a-input-password>
          </a-form-item>
        </a-tab-pane>
      </a-tabs>

      <a-form-item style="margin-top: 24px">
        <a-button
          size="large"
          type="primary"
          htmlType="submit"
          class="login-button"
          :loading="loginBtn"
          :disabled="loginBtn"
        >
          登录
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useUserStore } from '@/store'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const customActiveKey = ref('tab1')
const loginBtn = ref(false)
const isLoginError = ref(false)
const loginErrorMsg = ref('')

// 表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 表单引用
const formRef = ref()

// 切换标签页
const handleTabClick = (key) => {
  customActiveKey.value = key
}

// 提交登录
const handleSubmit = async () => {
  try {
    loginBtn.value = true
    isLoginError.value = false
    
    // 这里应该调用登录API
    // 暂时使用模拟登录
    if (loginForm.username === 'admin' && loginForm.password === '123456') {
      // 模拟登录成功
      const token = 'mock-token-' + Date.now()
      userStore.setToken(token)
      userStore.setUserInfo({
        username: loginForm.username,
        realname: '代理商管理员'
      })
      
      message.success('登录成功')
      
      // 跳转到主页
      router.push('/')
    } else {
      isLoginError.value = true
      loginErrorMsg.value = '用户名或密码错误'
    }
  } catch (error) {
    isLoginError.value = true
    loginErrorMsg.value = error.message || '登录失败'
  } finally {
    loginBtn.value = false
  }
}

onMounted(() => {
  // 如果已经登录，直接跳转到主页
  if (userStore.isLogin) {
    router.push('/')
  }
})
</script>

<style lang="less" scoped>
.main {
  min-width: 260px;
  width: 368px;
  margin: 0 auto;
}

.user-layout-login {
  label {
    font-size: 14px;
  }

  .getCaptcha {
    display: block;
    width: 100%;
    height: 40px;
  }

  .forge-password {
    font-size: 14px;
  }

  button.login-button {
    padding: 0 15px;
    font-size: 16px;
    height: 40px;
    width: 100%;
  }

  .user-login-other {
    text-align: left;
    margin-top: 24px;
    line-height: 22px;

    .item-icon {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.2);
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: #1890ff;
      }
    }

    .register {
      float: right;
    }
  }
}

.prefixIcon {
  color: rgba(0, 0, 0, 0.25);
}
</style>
