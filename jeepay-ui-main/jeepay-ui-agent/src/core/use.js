import 'ant-design-vue/dist/antd.css'
import { 
  Button, 
  Layout, 
  Menu, 
  Icon, 
  Drawer, 
  Radio, 
  Grid, 
  Form, 
  Input, 
  Select, 
  LocaleProvider, 
  Dropdown, 
  DatePicker, 
  TimePicker, 
  Popconfirm, 
  Table, 
  Modal, 
  message, 
  notification,
  Card,
  Tabs,
  Avatar,
  Badge,
  Popover,
  Tooltip,
  Tag,
  Divider,
  Switch,
  Upload,
  Progress,
  Spin,
  Alert,
  Tree,
  Pagination,
  Breadcrumb,
  Steps,
  Rate,
  Anchor,
  BackTop,
  Affix,
  Cascader,
  Checkbox,
  Transfer,
  Calendar,
  List,
  Statistic,
  Descriptions,
  Empty,
  Skeleton,
  Comment,
  ConfigProvider,
  Result,
  Space
} from 'ant-design-vue'

export function use(app) {
  app.use(Button)
  app.use(Layout)
  app.use(Menu)
  app.use(Icon)
  app.use(Drawer)
  app.use(Radio)
  app.use(Grid)
  app.use(Form)
  app.use(Input)
  app.use(Select)
  app.use(LocaleProvider)
  app.use(Dropdown)
  app.use(DatePicker)
  app.use(TimePicker)
  app.use(Popconfirm)
  app.use(Table)
  app.use(Modal)
  app.use(Card)
  app.use(Tabs)
  app.use(Avatar)
  app.use(Badge)
  app.use(Popover)
  app.use(Tooltip)
  app.use(Tag)
  app.use(Divider)
  app.use(Switch)
  app.use(Upload)
  app.use(Progress)
  app.use(Spin)
  app.use(Alert)
  app.use(Tree)
  app.use(Pagination)
  app.use(Breadcrumb)
  app.use(Steps)
  app.use(Rate)
  app.use(Anchor)
  app.use(BackTop)
  app.use(Affix)
  app.use(Cascader)
  app.use(Checkbox)
  app.use(Transfer)
  app.use(Calendar)
  app.use(List)
  app.use(Statistic)
  app.use(Descriptions)
  app.use(Empty)
  app.use(Skeleton)
  app.use(Comment)
  app.use(ConfigProvider)
  app.use(Result)
  app.use(Space)

  // 全局方法
  app.config.globalProperties.$message = message
  app.config.globalProperties.$notification = notification
  app.config.globalProperties.$info = Modal.info
  app.config.globalProperties.$success = Modal.success
  app.config.globalProperties.$error = Modal.error
  app.config.globalProperties.$warning = Modal.warning
  app.config.globalProperties.$confirm = Modal.confirm
}
