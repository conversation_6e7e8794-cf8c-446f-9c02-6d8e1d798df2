<template>
  <a-layout id="components-layout-demo-top-side-2">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      class="sider"
      width="256"
      :style="{ overflow: 'auto', height: '100vh', position: 'fixed', left: 0, zIndex: 10 }"
    >
      <div class="logo">
        <router-link to="/">
          <div style="width: 32px; height: 32px; background: #1890ff; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">J</div>
          <h1 v-show="!collapsed">Jeepay代理商</h1>
        </router-link>
      </div>
      
      <!-- 菜单 -->
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="dark"
        :inline-collapsed="collapsed"
        @click="handleMenuClick"
      >
        <template v-for="item in menuData" :key="item.key">
          <a-menu-item v-if="!item.children" :key="item.key">
            <span>{{ item.name }}</span>
          </a-menu-item>

          <a-sub-menu v-else :key="item.key">
            <template #title>{{ item.name }}</template>
            <a-menu-item v-for="child in item.children" :key="child.key">
              <span>{{ child.name }}</span>
            </a-menu-item>
          </a-sub-menu>
        </template>
      </a-menu>
    </a-layout-sider>
    
    <!-- 主体内容 -->
    <a-layout :style="{ marginLeft: collapsed ? '80px' : '256px' }">
      <!-- 顶部导航 -->
      <a-layout-header class="header" :style="{ background: '#fff', padding: 0 }">
        <div class="header-content">
          <div class="header-left">
            <menu-unfold-outlined
              v-if="collapsed"
              class="trigger"
              @click="() => (collapsed = !collapsed)"
            />
            <menu-fold-outlined
              v-else
              class="trigger"
              @click="() => (collapsed = !collapsed)"
            />
            
            <!-- 面包屑 -->
            <a-breadcrumb class="breadcrumb">
              <a-breadcrumb-item v-for="item in breadcrumbList" :key="item.path">
                <router-link v-if="item.path" :to="item.path">
                  {{ item.breadcrumbName }}
                </router-link>
                <span v-else>{{ item.breadcrumbName }}</span>
              </a-breadcrumb-item>
            </a-breadcrumb>
          </div>
          
          <div class="header-right">
            <!-- 用户信息 -->
            <a-dropdown>
              <a class="ant-dropdown-link" @click.prevent>
                <a-avatar :size="32" icon="user" />
                <span class="username">{{ userInfo.realname || '管理员' }}</span>
                <down-outlined />
              </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="userinfo">
                    <router-link to="/current/userinfo">
                      <user-outlined />
                      个人设置
                    </router-link>
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="logout" @click="handleLogout">
                    <logout-outlined />
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </a-layout-header>
      
      <!-- 内容区域 -->
      <a-layout-content class="content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  MenuUnfoldOutlined, 
  MenuFoldOutlined, 
  UserOutlined, 
  LogoutOutlined,
  DownOutlined 
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const collapsed = ref(false)
const selectedKeys = ref([])
const openKeys = ref([])

// 用户信息
const userInfo = reactive({
  realname: '代理商管理员'
})

// 菜单数据 - 这里应该从store获取
const menuData = ref([
  {
    key: 'MainPage',
    name: '主页',
    path: '/main'
  },
  {
    key: 'AgentMgr',
    name: '代理商管理',
    children: [
      { key: 'AgentInfoPage', name: '代理商信息', path: '/agent/info' },
      { key: 'SubAgentPage', name: '下级代理商', path: '/agent/sub' }
    ]
  },
  {
    key: 'MchMgr',
    name: '商户管理',
    children: [
      { key: 'MchListPage', name: '关联商户', path: '/mch/list' },
      { key: 'MchAppPage', name: '商户应用', path: '/mch/apps' }
    ]
  }
])

// 面包屑
const breadcrumbList = computed(() => {
  // 根据当前路由生成面包屑
  return [
    { breadcrumbName: '首页', path: '/' },
    { breadcrumbName: '当前页面' }
  ]
})

// 菜单点击处理
const handleMenuClick = ({ key }) => {
  // 根据key找到对应的路径并跳转
  const findPath = (items, targetKey) => {
    for (const item of items) {
      if (item.key === targetKey) {
        return item.path
      }
      if (item.children) {
        const childPath = findPath(item.children, targetKey)
        if (childPath) return childPath
      }
    }
    return null
  }
  
  const path = findPath(menuData.value, key)
  if (path) {
    router.push(path)
  }
}

// 退出登录
const handleLogout = () => {
  // 清除token等信息
  localStorage.removeItem('iToken')
  router.push('/user/login')
}

// 监听路由变化更新选中菜单
watch(route, (newRoute) => {
  // 根据当前路由设置选中的菜单项
  selectedKeys.value = [newRoute.name]
}, { immediate: true })

onMounted(() => {
  // 初始化选中菜单
  selectedKeys.value = [route.name]
})
</script>

<style lang="less" scoped>
#components-layout-demo-top-side-2 {
  height: 100vh;
}

.sider {
  .logo {
    height: 64px;
    padding: 16px;
    display: flex;
    align-items: center;
    
    a {
      display: flex;
      align-items: center;
      color: #fff;
      text-decoration: none;
    }
    
    img {
      height: 32px;
      margin-right: 12px;
    }
    
    h1 {
      color: #fff;
      font-size: 18px;
      margin: 0;
      font-weight: 600;
    }
  }
}

.header {
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
    padding: 0 24px;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    
    .trigger {
      font-size: 18px;
      line-height: 64px;
      padding: 0 24px;
      cursor: pointer;
      transition: color 0.3s;
      
      &:hover {
        color: #1890ff;
      }
    }
    
    .breadcrumb {
      margin-left: 24px;
    }
  }
  
  .header-right {
    .ant-dropdown-link {
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.85);
      text-decoration: none;
      
      .username {
        margin: 0 8px;
      }
    }
  }
}

.content {
  margin: 24px 16px 0;
  overflow: initial;
  
  .content-wrapper {
    padding: 24px;
    background: #fff;
    min-height: 360px;
  }
}
</style>
