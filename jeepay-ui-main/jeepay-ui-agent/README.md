# Jeepay代理商系统前端

基于Vue 3 + Ant Design Vue 4 + TypeScript + Vite构建的Jeepay代理商管理系统前端。

## 功能特性

- 🎯 **代理商管理** - 代理商信息管理、下级代理商管理
- 🏪 **商户管理** - 关联商户管理、商户应用管理
- 📊 **数据统计** - 实时数据展示、图表分析
- 💰 **分润管理** - 分润记录查看、分润统计
- 📋 **订单管理** - 支付订单、退款订单、转账订单管理
- 👥 **系统管理** - 用户管理、角色管理、权限管理
- 🔐 **安全认证** - JWT认证、权限控制
- 📱 **响应式设计** - 支持PC和移动端

## 技术栈

- **框架**: Vue 3.2.21
- **UI组件**: Ant Design Vue 4.2.6
- **构建工具**: Vite 2.6.13
- **语言**: TypeScript 4.4.3
- **状态管理**: Pinia 2.0.0
- **路由**: Vue Router 4.0.12
- **HTTP客户端**: Axios 0.20.0
- **图表**: ECharts 5.2.2
- **样式**: Less 4.1.2

## 项目结构

```
jeepay-ui-agent/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口
│   ├── assets/            # 资源文件
│   ├── components/        # 公共组件
│   ├── config/            # 配置文件
│   ├── core/              # 核心文件
│   ├── http/              # HTTP请求封装
│   ├── layouts/           # 布局组件
│   ├── router/            # 路由配置
│   ├── store/             # 状态管理
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   │   ├── agent/         # 代理商管理
│   │   ├── merchant/      # 商户管理
│   │   ├── order/         # 订单管理
│   │   ├── profit/        # 分润管理
│   │   ├── dashboard/     # 仪表板
│   │   ├── sysuser/       # 系统管理
│   │   └── ...
│   ├── App.vue            # 根组件
│   └── main.ts            # 入口文件
├── index.html             # HTML模板
├── package.json           # 依赖配置
├── vite.config.ts         # Vite配置
└── tsconfig.json          # TypeScript配置
```

## 开发指南

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发运行

```bash
npm run dev
# 或
yarn dev
```

访问 http://localhost:9219

### 构建部署

```bash
npm run build
# 或
yarn build
```

### 预览构建结果

```bash
npm run serve
# 或
yarn serve
```

## 配置说明

### 环境配置

项目支持多环境配置，可以在 `vite.config.ts` 中修改：

- 开发环境：http://localhost:9219
- 生产环境：/api

### API配置

在 `src/http/request.js` 中配置API基础地址和请求拦截器。

### 路由配置

在 `src/router/` 目录下配置路由，支持动态路由生成。

## 主要功能模块

### 1. 代理商管理
- 代理商信息查看和编辑
- 下级代理商管理
- 代理商统计数据

### 2. 商户管理
- 关联商户列表
- 商户应用管理
- 商户信息维护

### 3. 订单管理
- 支付订单查询
- 退款订单处理
- 转账订单管理

### 4. 分润管理
- 分润记录查看
- 分润统计报表

### 5. 系统管理
- 用户账号管理
- 角色权限配置

## 开发规范

### 代码规范
- 使用 ESLint + Prettier 进行代码格式化
- 遵循 Vue 3 Composition API 规范
- 使用 TypeScript 进行类型检查

### 组件规范
- 组件名使用 PascalCase
- 文件名使用 kebab-case
- 样式使用 scoped 作用域

### API规范
- 统一使用 async/await 处理异步请求
- 错误处理统一在拦截器中处理
- 接口返回格式统一

## 部署说明

### Docker部署

```bash
# 构建镜像
docker build -t jeepay-ui-agent .

# 运行容器
docker run -p 80:80 jeepay-ui-agent
```

### Nginx部署

将构建后的 `dist` 目录部署到Nginx服务器，配置示例：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://backend-server:9219;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 常见问题

### 1. 登录问题
- 检查后端API是否正常运行
- 确认用户名密码是否正确
- 查看浏览器控制台错误信息

### 2. 权限问题
- 确认用户角色权限配置
- 检查路由权限设置
- 查看API接口权限

### 3. 构建问题
- 清除node_modules重新安装依赖
- 检查Node.js版本是否符合要求
- 查看构建日志错误信息

## 更新日志

### v3.1.0 (2024-01-01)
- 初始版本发布
- 实现基础功能模块
- 支持代理商管理功能

## 许可证

本项目基于 LGPL-3.0 许可证开源。

## 联系我们

- 官网：https://www.jeequan.com
- 文档：https://doc.jeequan.com
- QQ群：635647058
