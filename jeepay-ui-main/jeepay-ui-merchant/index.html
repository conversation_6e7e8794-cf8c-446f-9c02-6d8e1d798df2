<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/indexImgs/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no"
    />
    <title>商户平台-Jeepay计全支付</title>
    <style>
      * {
        padding: 0;
        margin: 0;
      }

      .first-loading-wrp {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        min-height: 420px;
        height: 100vh;
      }
      .first-loading-wrp > h1 {
        font-size: 128px;
      }
      .first-loading-wrp .loading-wrp {
        padding: 98px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .dot {
        animation: antRotate 1.2s infinite linear;
        transform: rotate(45deg);
        position: relative;
        display: inline-block;
        font-size: 32px;
        width: 32px;
        height: 32px;
        box-sizing: border-box;
      }
      .dot i {
        width: 14px;
        height: 14px;
        position: absolute;
        display: block;
        background-color: #1890ff;
        border-radius: 100%;
        transform: scale(0.75);
        transform-origin: 50% 50%;
        opacity: 0.3;
        animation: antSpinMove 1s infinite linear alternate;
      }
      .dot i:nth-child(1) {
        top: 0;
        left: 0;
      }
      .dot i:nth-child(2) {
        top: 0;
        right: 0;
        -webkit-animation-delay: 0.4s;
        animation-delay: 0.4s;
      }
      .dot i:nth-child(3) {
        right: 0;
        bottom: 0;
        -webkit-animation-delay: 0.8s;
        animation-delay: 0.8s;
      }
      .dot i:nth-child(4) {
        bottom: 0;
        left: 0;
        -webkit-animation-delay: 1.2s;
        animation-delay: 1.2s;
      }
      @keyframes antRotate {
        to {
          -webkit-transform: rotate(405deg);
          transform: rotate(405deg);
        }
      }
      @-webkit-keyframes antRotate {
        to {
          -webkit-transform: rotate(405deg);
          transform: rotate(405deg);
        }
      }
      @keyframes antSpinMove {
        to {
          opacity: 1;
        }
      }
      @-webkit-keyframes antSpinMove {
        to {
          opacity: 1;
        }
      }
    </style>
  </head>

  <body>
    <div id="app">
      <!-- 默认页面的等待效果 -->
      <div class="first-loading-wrp">
        <div>
          <img
            src="/indexImgs/logo.svg"
            id="jeepay_index_logo_img"
            type="text/javascript"
            style="height: 50px"
            class="logo"
          />
        </div>
        <div class="loading-wrp">
          <span class="dot dot-spin">
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </span>
        </div>
        <div
          id="jeepay_index_logo_text"
          style="display: flex; justify-content: center; align-items: center"
        >
          计全科技
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
