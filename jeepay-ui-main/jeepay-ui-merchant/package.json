{"name": "jeepay-ui-merchant", "version": "3.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/icons-vue": "^7.0.1", "@soerenmartius/vue3-clipboard": "^0.1.2", "@wangeditor/core": "^0.14.0", "@wangeditor/editor": "^0.15.7", "@wangeditor/editor-for-vue": "5.1.8-11", "ant-design-vue": "^4.2.6", "axios": "^0.20.0-0", "bootstrap-icons": "^1.7.1", "echarts": "^5.2.2", "gm-crypto": "^0.1.8", "js-base64": "^3.7.2", "js-file-download": "^0.4.12", "moment": "^2.30.1", "nprogress": "^0.2.0", "pinia": "^2.0.0-rc.10", "qrcode.vue": "^3.3.3", "qs": "^6.10.1", "reconnectingwebsocket": "^1.0.0", "store": "^2.0.12", "v-viewer": "^3.0.10", "vue": "^3.2.21", "vue-router": "^4.0.12"}, "devDependencies": {"@antv/g2plot": "^2.4.32", "@types/crypto-js": "^4.0.2", "@types/node": "^16.11.12", "@types/qs": "^6.9.7", "@types/store": "^2.0.2", "@typescript-eslint/parser": "^5.4.0", "@vitejs/plugin-vue": "^1.9.4", "@vitejs/plugin-vue-jsx": "^1.3.3", "@vue/compiler-sfc": "^3.2.19", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "eslint": "^8.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^8.0.3", "less": "^4.1.2", "less-loader": "^10.2.0", "prettier": "^3.4.2", "typescript": "^4.4.3", "unplugin-vue-components": "^0.17.2", "vite": "^2.6.13", "vue-eslint-parser": "^8.0.1", "vue-tsc": "^0.3.0"}}